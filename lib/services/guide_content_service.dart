import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/guide/guide_section_data.dart';

/// Service for loading and parsing guide content from JSON files
class GuideContentService {
  static final GuideContentService _instance = GuideContentService._internal();
  factory GuideContentService() => _instance;
  GuideContentService._internal();

  // Cache for loaded content
  final Map<String, List<GuideSectionData>> _contentCache = {};
  final Map<String, Map<String, List<String>>> _searchIndex = {};

  /// Loads guide content from JSON files and converts to GuideSectionData objects
  Future<List<GuideSectionData>> loadGuideContent(String guideId) async {
    // Check cache first
    if (_contentCache.containsKey(guideId)) {
      return _contentCache[guideId]!;
    }

    try {
      // Load the main content file
      final contentPath = _getContentPath(guideId);
      final contentString = await rootBundle.loadString(contentPath);
      final contentJson = json.decode(contentString) as Map<String, dynamic>;

      // Parse JSON to sections
      final sections = parseJsonToSections(contentJson, guideId);
      
      // Cache the result
      _contentCache[guideId] = sections;
      
      // Build search index
      _buildSearchIndex(guideId, sections);
      
      return sections;
    } catch (e) {
      print('Error loading guide content for $guideId: $e');
      return [];
    }
  }

  /// Parses JSON content into GuideSectionData objects
  List<GuideSectionData> parseJsonToSections(
    Map<String, dynamic> json, 
    String guideId,
  ) {
    final sections = <GuideSectionData>[];
    
    // Handle different JSON structures
    if (json.containsKey('sections')) {
      // Standard structure with sections array
      final sectionsJson = json['sections'] as List<dynamic>;
      for (int i = 0; i < sectionsJson.length; i++) {
        final sectionJson = sectionsJson[i] as Map<String, dynamic>;
        final section = _parseSingleSection(sectionJson, guideId, i);
        sections.add(section);
      }
    } else if (json.containsKey('tabs')) {
      // Tab-based structure (legacy)
      final tabsJson = json['tabs'] as List<dynamic>;
      for (int i = 0; i < tabsJson.length; i++) {
        final tabJson = tabsJson[i] as Map<String, dynamic>;
        final section = _parseTabAsSection(tabJson, guideId, i);
        sections.add(section);
      }
    } else {
      // Direct content structure
      final section = _parseSingleSection(json, guideId, 0);
      sections.add(section);
    }
    
    return sections;
  }

  /// Parses a single section from JSON
  GuideSectionData _parseSingleSection(
    Map<String, dynamic> json, 
    String guideId, 
    int index,
  ) {
    final id = json['id'] ?? '${guideId}_section_$index';
    final title = json['title'] ?? 'Section ${index + 1}';
    final content = json['content'] ?? '';
    final items = List<String>.from(json['items'] ?? []);
    
    // Parse subsections if they exist
    final subsections = <GuideSubsection>[];
    if (json.containsKey('subsections')) {
      final subsectionsJson = json['subsections'] as List<dynamic>;
      for (final subsectionJson in subsectionsJson) {
        subsections.add(GuideSubsection.fromJson(subsectionJson));
      }
    }
    
    return GuideSectionData(
      id: id,
      title: title,
      content: content,
      items: items,
      subsections: subsections,
      type: _parseContentType(json['type']),
      estimatedReadTime: json['estimatedReadTime'] ?? _calculateReadTime(content, items),
      metadata: json['metadata'],
    );
  }

  /// Parses a tab structure as a section (for legacy compatibility)
  GuideSectionData _parseTabAsSection(
    Map<String, dynamic> json, 
    String guideId, 
    int index,
  ) {
    final id = json['id'] ?? '${guideId}_tab_$index';
    final title = json['title'] ?? json['label'] ?? 'Tab ${index + 1}';
    
    // Extract content from tab structure
    String content = '';
    List<String> items = [];
    
    if (json.containsKey('content')) {
      if (json['content'] is String) {
        content = json['content'];
      } else if (json['content'] is Map) {
        final contentMap = json['content'] as Map<String, dynamic>;
        content = contentMap['text'] ?? '';
        items = List<String>.from(contentMap['items'] ?? []);
      }
    }
    
    return GuideSectionData(
      id: id,
      title: title,
      content: content,
      items: items,
      type: _parseContentType(json['type']),
      estimatedReadTime: _calculateReadTime(content, items),
    );
  }

  /// Builds search index for efficient searching
  void _buildSearchIndex(String guideId, List<GuideSectionData> sections) {
    final index = <String, List<String>>{};
    
    for (final section in sections) {
      final words = _extractSearchableWords(section);
      for (final word in words) {
        if (word.length > 2) { // Only index words longer than 2 characters
          index.putIfAbsent(word, () => []).add(section.id);
        }
      }
    }
    
    _searchIndex[guideId] = index;
  }

  /// Searches for sections matching the query
  List<GuideSectionData> searchSections(String guideId, String query) {
    final sections = _contentCache[guideId] ?? [];
    final index = _searchIndex[guideId] ?? {};
    
    if (query.isEmpty) return sections;
    
    final queryWords = query.toLowerCase().split(' ')
        .where((word) => word.length > 2)
        .toList();
    
    final matchingSectionIds = <String>{};
    
    // Find sections that match any query word
    for (final word in queryWords) {
      // Exact matches
      if (index.containsKey(word)) {
        matchingSectionIds.addAll(index[word]!);
      }
      
      // Partial matches (fuzzy search)
      for (final indexWord in index.keys) {
        if (indexWord.contains(word) || word.contains(indexWord)) {
          matchingSectionIds.addAll(index[indexWord]!);
        }
      }
    }
    
    // Return matching sections in original order
    return sections.where((section) => matchingSectionIds.contains(section.id)).toList();
  }

  /// Extracts searchable words from a section
  List<String> _extractSearchableWords(GuideSectionData section) {
    final words = <String>[];
    
    // Add words from title, content, and items
    words.addAll(_splitIntoWords(section.title));
    words.addAll(_splitIntoWords(section.content));
    
    for (final item in section.items) {
      words.addAll(_splitIntoWords(item));
    }
    
    // Add words from subsections
    for (final subsection in section.subsections) {
      words.addAll(_splitIntoWords(subsection.title));
      words.addAll(_splitIntoWords(subsection.content));
      for (final item in subsection.items) {
        words.addAll(_splitIntoWords(item));
      }
    }
    
    return words.map((word) => word.toLowerCase()).toList();
  }

  /// Splits text into individual words
  List<String> _splitIntoWords(String text) {
    return text
        .replaceAll(RegExp(r'[^\w\s]'), ' ') // Replace punctuation with spaces
        .split(RegExp(r'\s+')) // Split on whitespace
        .where((word) => word.isNotEmpty)
        .toList();
  }

  /// Gets the content file path for a guide
  String _getContentPath(String guideId) {
    // Map guide IDs to their content file paths
    switch (guideId) {
      case 'ir':
        return 'assets/ir/presentation.json';
      case 'is':
        return 'assets/is/presentation.json';
      case 'comptabilite_generale':
        return 'assets/compta_generale/introduction.json';
      case 'droits_enregistrement':
        return 'assets/fiscalite/droits_enregistrement.json';
      default:
        return 'assets/$guideId/content.json';
    }
  }

  /// Parses content type from string
  GuideSectionType _parseContentType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'list':
        return GuideSectionType.list;
      case 'calculator':
        return GuideSectionType.calculator;
      case 'example':
        return GuideSectionType.example;
      case 'definition':
        return GuideSectionType.definition;
      case 'formula':
        return GuideSectionType.formula;
      case 'table':
        return GuideSectionType.table;
      case 'widget':
        return GuideSectionType.widget;
      default:
        return GuideSectionType.text;
    }
  }

  /// Calculates estimated read time based on content length
  int _calculateReadTime(String content, List<String> items) {
    final wordCount = content.split(' ').length + 
                     items.fold(0, (sum, item) => sum + item.split(' ').length);
    // Average reading speed: 200 words per minute
    return (wordCount / 200).ceil().clamp(1, 30);
  }

  /// Clears the content cache
  void clearCache() {
    _contentCache.clear();
    _searchIndex.clear();
  }

  /// Gets cached content without loading
  List<GuideSectionData>? getCachedContent(String guideId) {
    return _contentCache[guideId];
  }
}
