import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/guide/interactive_step_data.dart';

/// Service for managing interactive content and walkthroughs
class InteractiveContentService {
  static InteractiveContentService? _instance;
  static InteractiveContentService get instance => _instance ??= InteractiveContentService._();
  
  InteractiveContentService._();
  
  // Cache for loaded content
  final Map<String, Map<String, dynamic>> _contentCache = {};
  final Map<String, List<InteractiveStepData>> _stepsCache = {};
  
  /// Load interactive content for a specific guide and section
  Future<Map<String, dynamic>?> loadInteractiveContent(String guideId, String sectionId) async {
    try {
      final cacheKey = '${guideId}_$sectionId';
      
      // Return cached content if available
      if (_contentCache.containsKey(cacheKey)) {
        return _contentCache[cacheKey];
      }
      
      // Load from assets
      final assetPath = 'assets/guides/$guideId/interactive/$sectionId.json';
      final String jsonString = await rootBundle.loadString(assetPath);
      final Map<String, dynamic> content = json.decode(jsonString);
      
      // Cache the content
      _contentCache[cacheKey] = content;
      
      return content;
    } catch (e) {
      // Return null if content doesn't exist or fails to load
      return null;
    }
  }
  
  /// Load interactive steps for a walkthrough
  Future<List<InteractiveStepData>> loadInteractiveSteps(String guideId, String walkthroughId) async {
    try {
      final cacheKey = '${guideId}_$walkthroughId';
      
      // Return cached steps if available
      if (_stepsCache.containsKey(cacheKey)) {
        return _stepsCache[cacheKey]!;
      }
      
      // Load content and extract steps
      final content = await loadInteractiveContent(guideId, walkthroughId);
      if (content == null) return [];
      
      final stepsData = content['steps'] as List<dynamic>? ?? [];
      final steps = stepsData
          .map((stepJson) => InteractiveStepData.fromJson(stepJson as Map<String, dynamic>))
          .toList();
      
      // Cache the steps
      _stepsCache[cacheKey] = steps;
      
      return steps;
    } catch (e) {
      return [];
    }
  }
  
  /// Get simulation scenario data
  Future<Map<String, dynamic>?> getSimulationScenario(String guideId, String simulationId) async {
    try {
      final content = await loadInteractiveContent(guideId, simulationId);
      return content?['scenario'] as Map<String, dynamic>?;
    } catch (e) {
      return null;
    }
  }
  
  /// Get diagram configuration
  Future<Map<String, dynamic>?> getDiagramConfig(String guideId, String diagramId) async {
    try {
      final content = await loadInteractiveContent(guideId, diagramId);
      return content?['diagram'] as Map<String, dynamic>?;
    } catch (e) {
      return null;
    }
  }
  
  /// Check if interactive content exists
  Future<bool> hasInteractiveContent(String guideId, String sectionId) async {
    try {
      final content = await loadInteractiveContent(guideId, sectionId);
      return content != null;
    } catch (e) {
      return false;
    }
  }
  
  /// Get available interactive content types for a section
  Future<List<InteractiveContentType>> getAvailableContentTypes(String guideId, String sectionId) async {
    try {
      final content = await loadInteractiveContent(guideId, sectionId);
      if (content == null) return [];
      
      final List<InteractiveContentType> types = [];
      
      if (content.containsKey('steps')) {
        types.add(InteractiveContentType.walkthrough);
      }
      
      if (content.containsKey('scenario')) {
        types.add(InteractiveContentType.simulation);
      }
      
      if (content.containsKey('diagram')) {
        types.add(InteractiveContentType.diagram);
      }
      
      if (content.containsKey('quiz')) {
        types.add(InteractiveContentType.quiz);
      }
      
      if (content.containsKey('calculator')) {
        types.add(InteractiveContentType.calculator);
      }
      
      return types;
    } catch (e) {
      return [];
    }
  }
  
  /// Validate user input for a step
  Future<ValidationResult> validateStepInput(
    InteractiveStepData step,
    dynamic userInput,
  ) async {
    try {
      return step.validateUserInput(userInput);
    } catch (e) {
      return ValidationResult(
        isValid: false,
        message: 'Erreur de validation: $e',
      );
    }
  }
  
  /// Get hints for a step
  Future<List<String>> getStepHints(InteractiveStepData step) async {
    return step.hints;
  }
  
  /// Get step visual elements (images, videos, etc.)
  Future<Map<String, dynamic>> getStepVisualElements(InteractiveStepData step) async {
    return step.visualElements;
  }
  
  /// Track user progress through interactive content
  Future<void> trackProgress(
    String guideId,
    String contentId,
    String stepId,
    double score,
  ) async {
    // This would typically save to a progress service or analytics
    // For now, we'll just print for debugging
    // TODO: Implement actual progress tracking
    // print('Progress tracked: $guideId/$contentId/$stepId - Score: $score');
  }
  
  /// Get user's progress for interactive content
  Future<Map<String, double>> getUserProgress(String guideId, String contentId) async {
    // This would typically load from a progress service
    // For now, return empty map
    return {};
  }
  
  /// Generate dynamic content based on user's previous answers
  Future<List<InteractiveStepData>> generateAdaptiveSteps(
    String guideId,
    String baseContentId,
    Map<String, dynamic> userHistory,
  ) async {
    try {
      final baseSteps = await loadInteractiveSteps(guideId, baseContentId);
      
      // For now, return base steps
      // In a full implementation, this would adapt content based on user history
      return baseSteps;
    } catch (e) {
      return [];
    }
  }
  
  /// Create custom walkthrough from template
  Future<List<InteractiveStepData>> createCustomWalkthrough(
    String templateType,
    Map<String, dynamic> parameters,
  ) async {
    try {
      // Load template
      final templatePath = 'assets/templates/interactive/$templateType.json';
      final String templateString = await rootBundle.loadString(templatePath);
      final Map<String, dynamic> template = json.decode(templateString);
      
      // Generate steps from template with parameters
      final stepsData = template['steps'] as List<dynamic>? ?? [];
      final steps = stepsData
          .map((stepJson) => _processStepTemplate(stepJson, parameters))
          .map((processedJson) => InteractiveStepData.fromJson(processedJson))
          .toList();
      
      return steps;
    } catch (e) {
      return [];
    }
  }
  
  /// Process step template with parameters
  Map<String, dynamic> _processStepTemplate(
    dynamic stepTemplate,
    Map<String, dynamic> parameters,
  ) {
    final stepJson = Map<String, dynamic>.from(stepTemplate as Map);
    
    // Replace template variables with actual values
    _replaceTemplateVariables(stepJson, parameters);
    
    return stepJson;
  }
  
  /// Replace template variables recursively
  void _replaceTemplateVariables(
    Map<String, dynamic> data,
    Map<String, dynamic> parameters,
  ) {
    data.forEach((key, value) {
      if (value is String) {
        // Replace template variables like {{variable}}
        String newValue = value;
        parameters.forEach((paramKey, paramValue) {
          newValue = newValue.replaceAll('{{$paramKey}}', paramValue.toString());
        });
        data[key] = newValue;
      } else if (value is Map<String, dynamic>) {
        _replaceTemplateVariables(value, parameters);
      } else if (value is List) {
        for (int i = 0; i < value.length; i++) {
          if (value[i] is Map<String, dynamic>) {
            _replaceTemplateVariables(value[i], parameters);
          } else if (value[i] is String) {
            String newValue = value[i];
            parameters.forEach((paramKey, paramValue) {
              newValue = newValue.replaceAll('{{$paramKey}}', paramValue.toString());
            });
            value[i] = newValue;
          }
        }
      }
    });
  }
  
  /// Export user's interactive progress
  Future<Map<String, dynamic>> exportInteractiveProgress(String userId) async {
    // This would export all interactive progress for a user
    return {
      'version': '1.0',
      'exportDate': DateTime.now().toIso8601String(),
      'userId': userId,
      'progress': {}, // Would contain actual progress data
    };
  }
  
  /// Clear cache
  void clearCache() {
    _contentCache.clear();
    _stepsCache.clear();
  }
  
  /// Preload content for better performance
  Future<void> preloadContent(String guideId, List<String> sectionIds) async {
    final futures = sectionIds.map((sectionId) => loadInteractiveContent(guideId, sectionId));
    await Future.wait(futures);
  }
}

/// Types of interactive content
enum InteractiveContentType {
  walkthrough,
  simulation,
  diagram,
  quiz,
  calculator,
}

/// Extension to get content type labels
extension InteractiveContentTypeExtension on InteractiveContentType {
  String get label {
    switch (this) {
      case InteractiveContentType.walkthrough:
        return 'Parcours guidé';
      case InteractiveContentType.simulation:
        return 'Simulation';
      case InteractiveContentType.diagram:
        return 'Diagramme interactif';
      case InteractiveContentType.quiz:
        return 'Quiz';
      case InteractiveContentType.calculator:
        return 'Calculatrice';
    }
  }
  
  String get icon {
    switch (this) {
      case InteractiveContentType.walkthrough:
        return 'walk';
      case InteractiveContentType.simulation:
        return 'play_circle';
      case InteractiveContentType.diagram:
        return 'schema';
      case InteractiveContentType.quiz:
        return 'quiz';
      case InteractiveContentType.calculator:
        return 'calculate';
    }
  }
}