import 'package:flutter/material.dart';
import '../../models/guide/guide_section_data.dart';
import '../../theme/semantic_colors.dart';

/// Visual difficulty indicator widget that displays content difficulty levels and prerequisites
class DifficultyIndicator extends StatelessWidget {
  final GuideSectionData sectionData;
  final bool isCompact;
  final bool showPrerequisites;
  final bool showEstimatedTime;

  const DifficultyIndicator({
    super.key,
    required this.sectionData,
    this.isCompact = false,
    this.showPrerequisites = true,
    this.showEstimatedTime = true,
  });

  @override
  Widget build(BuildContext context) {
    if (isCompact) {
      return _buildCompactView(context);
    } else {
      return _buildDetailedView(context);
    }
  }

  Widget _buildCompactView(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildDifficultyStars(context, compact: true),
        const SizedBox(width: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: sectionData.getDifficultyColor().withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: sectionData.getDifficultyColor().withOpacity(0.3),
            ),
          ),
          child: Text(
            sectionData.getDifficultyLabel(),
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: sectionData.getDifficultyColor(),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        if (sectionData.prerequisites.isNotEmpty) ...[
          const SizedBox(width: 4),
          Icon(
            Icons.lock_outline,
            size: 14,
            color: Colors.orange,
          ),
        ],
      ],
    );
  }

  Widget _buildDetailedView(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: sectionData.getDifficultyColor().withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const SizedBox(height: 16),
          _buildDifficultyDetails(context),
          if (showEstimatedTime) ...[
            const SizedBox(height: 12),
            _buildEstimatedTime(context),
          ],
          if (sectionData.skillsRequired.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildRequiredSkills(context),
          ],
          if (showPrerequisites && sectionData.prerequisites.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildPrerequisites(context),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: sectionData.getDifficultyColor().withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getDifficultyIcon(),
            color: sectionData.getDifficultyColor(),
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Niveau de Difficulté',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: sectionData.getDifficultyColor(),
                ),
              ),
              Text(
                'Évaluation de la complexité du contenu',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDifficultyDetails(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    sectionData.getDifficultyLabel(),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: sectionData.getDifficultyColor(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildDifficultyStars(context),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                _getDifficultyDescription(),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: sectionData.getDifficultyColor().withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: sectionData.getDifficultyColor().withOpacity(0.3),
            ),
          ),
          child: Column(
            children: [
              Text(
                '${sectionData.estimatedDifficulty}',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: sectionData.getDifficultyColor(),
                ),
              ),
              Text(
                'sur 5',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: sectionData.getDifficultyColor(),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDifficultyStars(BuildContext context, {bool compact = false}) {
    final starSize = compact ? 12.0 : 16.0;
    final difficultyLevel = sectionData.estimatedDifficulty;
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        final isFilled = index < difficultyLevel;
        return Icon(
          isFilled ? Icons.star : Icons.star_outline,
          size: starSize,
          color: isFilled 
              ? sectionData.getDifficultyColor()
              : Theme.of(context).colorScheme.outline,
        );
      }),
    );
  }

  Widget _buildEstimatedTime(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 16,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          'Temps estimé: ${sectionData.estimatedReadTime} min',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildRequiredSkills(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.psychology,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              'Compétences requises:',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 6,
          runSpacing: 6,
          children: sectionData.skillsRequired.map((skill) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                ),
              ),
              child: Text(
                skill,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPrerequisites(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.school,
              size: 16,
              color: Colors.orange,
            ),
            const SizedBox(width: 8),
            Text(
              'Prérequis:',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.orange[700],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Column(
          children: sectionData.prerequisites.map((prerequisite) {
            return Container(
              margin: const EdgeInsets.only(bottom: 6),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: Colors.orange.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.arrow_right,
                    size: 16,
                    color: Colors.orange[700],
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      prerequisite,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.orange[800],
                      ),
                    ),
                  ),
                  Icon(
                    Icons.open_in_new,
                    size: 14,
                    color: Colors.orange[600],
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  IconData _getDifficultyIcon() {
    switch (sectionData.difficultyLevel) {
      case DifficultyLevel.beginner:
        return Icons.school;
      case DifficultyLevel.intermediate:
        return Icons.trending_up;
      case DifficultyLevel.advanced:
        return Icons.psychology;
    }
  }

  String _getDifficultyDescription() {
    switch (sectionData.difficultyLevel) {
      case DifficultyLevel.beginner:
        return 'Concepts de base, accessible aux débutants';
      case DifficultyLevel.intermediate:
        return 'Concepts modérés, nécessite des connaissances préalables';
      case DifficultyLevel.advanced:
        return 'Concepts avancés, expertise recommandée';
    }
  }

  /// Factory method to create a compact difficulty indicator
  static Widget compact(GuideSectionData sectionData) {
    return DifficultyIndicator(
      sectionData: sectionData,
      isCompact: true,
      showPrerequisites: false,
      showEstimatedTime: false,
    );
  }

  /// Factory method to create a detailed difficulty indicator
  static Widget detailed(GuideSectionData sectionData) {
    return DifficultyIndicator(
      sectionData: sectionData,
      isCompact: false,
      showPrerequisites: true,
      showEstimatedTime: true,
    );
  }
}