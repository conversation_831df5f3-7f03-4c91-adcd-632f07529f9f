import 'package:flutter/material.dart';
import '../../../theme/semantic_colors.dart';

/// Double-entry bookkeeping diagram widget that visualizes T-accounts and transactions
class DoubleEntryDiagramWidget extends StatefulWidget {
  final Map<String, dynamic> schema;
  final bool isInteractive;
  final VoidCallback? onComplete;

  const DoubleEntryDiagramWidget({
    super.key,
    required this.schema,
    this.isInteractive = true,
    this.onComplete,
  });

  @override
  State<DoubleEntryDiagramWidget> createState() => _DoubleEntryDiagramWidgetState();
}

class _DoubleEntryDiagramWidgetState extends State<DoubleEntryDiagramWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  int _currentTransactionIndex = 0;
  List<AccountingTransaction> _transactions = [];
  Map<String, TAccount> _accounts = {};
  bool _isAnimating = false;
  bool _showBalance = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    _parseSchemaData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _parseSchemaData() {
    final data = widget.schema['data'] as Map<String, dynamic>? ?? {};
    
    // Parse accounts
    final accountsData = data['accounts'] as Map<String, dynamic>? ?? {};
    accountsData.forEach((key, value) {
      final accountData = value as Map<String, dynamic>;
      _accounts[key] = TAccount(
        name: accountData['name'] as String? ?? key,
        type: _parseAccountType(accountData['type'] as String?),
        initialBalance: (accountData['initialBalance'] as num?)?.toDouble() ?? 0,
      );
    });

    // Parse transactions
    final transactionsData = data['transactions'] as List<dynamic>? ?? [];
    _transactions = transactionsData.map((transaction) {
      final t = transaction as Map<String, dynamic>;
      return AccountingTransaction(
        id: t['id'] as String? ?? '',
        description: t['description'] as String? ?? '',
        date: t['date'] as String? ?? '',
        entries: (t['entries'] as List<dynamic>?)?.map((entry) {
          final e = entry as Map<String, dynamic>;
          return JournalEntry(
            accountName: e['account'] as String? ?? '',
            debit: (e['debit'] as num?)?.toDouble(),
            credit: (e['credit'] as num?)?.toDouble(),
            description: e['description'] as String? ?? '',
          );
        }).toList() ?? [],
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SemanticColors.surfaceVariant(context).withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: SemanticColors.outline(context).withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildAccountingEquation(),
          const SizedBox(height: 20),
          _buildTAccounts(),
          const SizedBox(height: 16),
          if (widget.isInteractive) _buildControls(),
          if (_currentTransactionIndex > 0) _buildTransactionSummary(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.account_balance,
          color: SemanticColors.primary(context),
          size: 28,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Comptabilité en Partie Double',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: SemanticColors.primary(context),
                ),
              ),
              Text(
                'Visualisation des comptes en T et des écritures',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: SemanticColors.onSurfaceVariant(context),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAccountingEquation() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SemanticColors.primaryContainer(context).withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: SemanticColors.primary(context).withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Text(
            'Équation Comptable Fondamentale',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: SemanticColors.primary(context),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildEquationBox('ACTIF', Colors.blue),
              const SizedBox(width: 16),
              Text(
                '=',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: SemanticColors.primary(context),
                ),
              ),
              const SizedBox(width: 16),
              _buildEquationBox('PASSIF', Colors.orange),
              const SizedBox(width: 8),
              Text(
                '+',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: SemanticColors.primary(context),
                ),
              ),
              const SizedBox(width: 8),
              _buildEquationBox('CAPITAUX PROPRES', Colors.green),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEquationBox(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Text(
        label,
        style: Theme.of(context).textTheme.labelMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: color.withOpacity(0.8),
        ),
      ),
    );
  }

  Widget _buildTAccounts() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _accounts.entries.map((entry) {
          return Container(
            margin: const EdgeInsets.only(right: 16),
            child: _buildTAccount(entry.key, entry.value),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTAccount(String accountId, TAccount account) {
    final debits = _getAccountDebits(accountId);
    final credits = _getAccountCredits(accountId);
    final balance = _calculateAccountBalance(account, debits, credits);

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: 200,
          decoration: BoxDecoration(
            color: SemanticColors.surface(context),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: SemanticColors.outline(context),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Account header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getAccountColor(account.type).withOpacity(0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(6),
                    topRight: Radius.circular(6),
                  ),
                  border: Border(
                    bottom: BorderSide(
                      color: SemanticColors.outline(context),
                      width: 2,
                    ),
                  ),
                ),
                child: Text(
                  account.name,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _getAccountColor(account.type),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              // T-Account body
              Row(
                children: [
                  // Debit side
                  Expanded(
                    child: Container(
                      height: 200,
                      decoration: BoxDecoration(
                        border: Border(
                          right: BorderSide(
                            color: SemanticColors.outline(context),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Column(
                        children: [
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.1),
                              border: Border(
                                bottom: BorderSide(
                                  color: SemanticColors.outline(context).withOpacity(0.5),
                                ),
                              ),
                            ),
                            child: Text(
                              'DÉBIT',
                              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.green[700],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Expanded(
                            child: ListView.builder(
                              padding: const EdgeInsets.all(4),
                              itemCount: debits.length,
                              itemBuilder: (context, index) {
                                final debit = debits[index];
                                final isVisible = index < _getVisibleEntriesCount();
                                return AnimatedOpacity(
                                  opacity: isVisible ? 1.0 : 0.0,
                                  duration: const Duration(milliseconds: 300),
                                  child: Container(
                                    margin: const EdgeInsets.only(bottom: 2),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 4, 
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: isVisible 
                                          ? Colors.green.withOpacity(0.1)
                                          : Colors.transparent,
                                      borderRadius: BorderRadius.circular(3),
                                    ),
                                    child: Text(
                                      '${debit.toStringAsFixed(2)}',
                                      style: Theme.of(context).textTheme.bodySmall,
                                      textAlign: TextAlign.right,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Credit side
                  Expanded(
                    child: Container(
                      height: 200,
                      child: Column(
                        children: [
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.1),
                              border: Border(
                                bottom: BorderSide(
                                  color: SemanticColors.outline(context).withOpacity(0.5),
                                ),
                              ),
                            ),
                            child: Text(
                              'CRÉDIT',
                              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.red[700],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Expanded(
                            child: ListView.builder(
                              padding: const EdgeInsets.all(4),
                              itemCount: credits.length,
                              itemBuilder: (context, index) {
                                final credit = credits[index];
                                final isVisible = index < _getVisibleEntriesCount();
                                return AnimatedOpacity(
                                  opacity: isVisible ? 1.0 : 0.0,
                                  duration: const Duration(milliseconds: 300),
                                  child: Container(
                                    margin: const EdgeInsets.only(bottom: 2),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 4,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: isVisible 
                                          ? Colors.red.withOpacity(0.1)
                                          : Colors.transparent,
                                      borderRadius: BorderRadius.circular(3),
                                    ),
                                    child: Text(
                                      '${credit.toStringAsFixed(2)}',
                                      style: Theme.of(context).textTheme.bodySmall,
                                      textAlign: TextAlign.right,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              // Balance
              if (_showBalance)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: SemanticColors.primaryContainer(context).withOpacity(0.3),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(6),
                      bottomRight: Radius.circular(6),
                    ),
                    border: Border(
                      top: BorderSide(
                        color: SemanticColors.outline(context),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'SOLDE',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: SemanticColors.primary(context),
                        ),
                      ),
                      Text(
                        '${balance.abs().toStringAsFixed(2)} ${balance >= 0 ? 'D' : 'C'}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: SemanticColors.primary(context),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton.icon(
          onPressed: _currentTransactionIndex > 0 ? _previousTransaction : null,
          icon: const Icon(Icons.skip_previous),
          label: const Text('Précédent'),
          style: ElevatedButton.styleFrom(
            backgroundColor: SemanticColors.secondary(context),
          ),
        ),
        const SizedBox(width: 16),
        ElevatedButton.icon(
          onPressed: _isAnimating ? null : _nextTransaction,
          icon: _isAnimating 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.skip_next),
          label: Text(_currentTransactionIndex >= _transactions.length 
              ? 'Voir les soldes' 
              : 'Transaction suivante'),
          style: ElevatedButton.styleFrom(
            backgroundColor: SemanticColors.primary(context),
            foregroundColor: SemanticColors.onPrimary(context),
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionSummary() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SemanticColors.primaryContainer(context).withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: SemanticColors.primary(context).withOpacity(0.5),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Résumé des Transactions',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: SemanticColors.primary(context),
            ),
          ),
          const SizedBox(height: 8),
          ...List.generate(
            _currentTransactionIndex.clamp(0, _transactions.length),
            (index) => _buildTransactionSummaryItem(_transactions[index]),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionSummaryItem(AccountingTransaction transaction) {
    final totalDebit = transaction.entries.fold<double>(
      0, (sum, entry) => sum + (entry.debit ?? 0),
    );
    final totalCredit = transaction.entries.fold<double>(
      0, (sum, entry) => sum + (entry.credit ?? 0),
    );
    final isBalanced = (totalDebit - totalCredit).abs() < 0.01;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: SemanticColors.surface(context),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: isBalanced ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  transaction.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Icon(
                isBalanced ? Icons.check_circle : Icons.error,
                color: isBalanced ? Colors.green : Colors.red,
                size: 16,
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Total Débit: ${totalDebit.toStringAsFixed(2)} | Total Crédit: ${totalCredit.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: SemanticColors.onSurfaceVariant(context),
            ),
          ),
        ],
      ),
    );
  }

  void _nextTransaction() {
    if (_currentTransactionIndex >= _transactions.length) {
      setState(() {
        _showBalance = true;
      });
      widget.onComplete?.call();
      return;
    }

    setState(() {
      _isAnimating = true;
      _currentTransactionIndex++;
    });

    _animationController.forward().then((_) {
      setState(() {
        _isAnimating = false;
      });
      _animationController.reset();
    });
  }

  void _previousTransaction() {
    if (_currentTransactionIndex > 0) {
      setState(() {
        _currentTransactionIndex--;
        _showBalance = false;
      });
    }
  }

  List<double> _getAccountDebits(String accountId) {
    final debits = <double>[];
    for (int i = 0; i < _currentTransactionIndex && i < _transactions.length; i++) {
      final transaction = _transactions[i];
      for (final entry in transaction.entries) {
        if (entry.accountName == accountId && entry.debit != null) {
          debits.add(entry.debit!);
        }
      }
    }
    return debits;
  }

  List<double> _getAccountCredits(String accountId) {
    final credits = <double>[];
    for (int i = 0; i < _currentTransactionIndex && i < _transactions.length; i++) {
      final transaction = _transactions[i];
      for (final entry in transaction.entries) {
        if (entry.accountName == accountId && entry.credit != null) {
          credits.add(entry.credit!);
        }
      }
    }
    return credits;
  }

  double _calculateAccountBalance(TAccount account, List<double> debits, List<double> credits) {
    final totalDebits = debits.fold<double>(0, (sum, debit) => sum + debit);
    final totalCredits = credits.fold<double>(0, (sum, credit) => sum + credit);
    
    switch (account.type) {
      case AccountType.asset:
      case AccountType.expense:
        return account.initialBalance + totalDebits - totalCredits;
      case AccountType.liability:
      case AccountType.equity:
      case AccountType.revenue:
        return account.initialBalance + totalCredits - totalDebits;
    }
  }

  int _getVisibleEntriesCount() {
    return (_animation.value * 10).round();
  }

  Color _getAccountColor(AccountType type) {
    switch (type) {
      case AccountType.asset:
        return Colors.blue;
      case AccountType.liability:
        return Colors.orange;
      case AccountType.equity:
        return Colors.green;
      case AccountType.revenue:
        return Colors.purple;
      case AccountType.expense:
        return Colors.red;
    }
  }

  AccountType _parseAccountType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'asset':
      case 'actif':
        return AccountType.asset;
      case 'liability':
      case 'passif':
        return AccountType.liability;
      case 'equity':
      case 'capitaux':
        return AccountType.equity;
      case 'revenue':
      case 'produit':
        return AccountType.revenue;
      case 'expense':
      case 'charge':
        return AccountType.expense;
      default:
        return AccountType.asset;
    }
  }
}

/// Enumeration for account types
enum AccountType {
  asset,
  liability,
  equity,
  revenue,
  expense,
}

/// Data model for T-Account
class TAccount {
  final String name;
  final AccountType type;
  final double initialBalance;

  const TAccount({
    required this.name,
    required this.type,
    this.initialBalance = 0,
  });
}

/// Data model for accounting transactions
class AccountingTransaction {
  final String id;
  final String description;
  final String date;
  final List<JournalEntry> entries;

  const AccountingTransaction({
    required this.id,
    required this.description,
    required this.date,
    required this.entries,
  });
}

/// Data model for journal entries
class JournalEntry {
  final String accountName;
  final double? debit;
  final double? credit;
  final String description;

  const JournalEntry({
    required this.accountName,
    this.debit,
    this.credit,
    this.description = '',
  });
}