import 'package:flutter/material.dart';
import '../../models/guide/bookmark_data.dart';
import '../../theme/design_tokens.dart';

/// Bookmark button widget for adding/removing bookmarks from guide sections
class BookmarkButton extends StatefulWidget {
  final String guideId;
  final String sectionId;
  final String sectionTitle;
  final bool isBookmarked;
  final Function(BookmarkData)? onBookmarkAdded;
  final Function(String)? onBookmarkRemoved;
  final BookmarkType bookmarkType;
  final bool showLabel;
  final bool isCompact;

  const BookmarkButton({
    super.key,
    required this.guideId,
    required this.sectionId,
    required this.sectionTitle,
    required this.isBookmarked,
    this.onBookmarkAdded,
    this.onBookmarkRemoved,
    this.bookmarkType = BookmarkType.section,
    this.showLabel = false,
    this.isCompact = false,
  });

  @override
  State<BookmarkButton> createState() => _BookmarkButtonState();
}

class _BookmarkButtonState extends State<BookmarkButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  
  bool _isAnimating = false;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }
  
  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.5, curve: Curves.elasticOut),
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeInOut),
    ));
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    if (widget.isCompact) {
      return _buildCompactButton(context);
    } else if (widget.showLabel) {
      return _buildLabeledButton(context);
    } else {
      return _buildIconButton(context);
    }
  }
  
  Widget _buildCompactButton(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: GestureDetector(
              onTap: _toggleBookmark,
              onLongPress: _showBookmarkOptions,
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: widget.isBookmarked 
                      ? Theme.of(context).colorScheme.primary
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: widget.isBookmarked
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.outline.withOpacity(0.5),
                  ),
                ),
                child: Icon(
                  widget.isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                  size: 16,
                  color: widget.isBookmarked
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildIconButton(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: IconButton(
              onPressed: _isAnimating ? null : _toggleBookmark,
              onLongPress: _showBookmarkOptions,
              tooltip: widget.isBookmarked 
                  ? 'Retirer le signet' 
                  : 'Ajouter un signet',
              icon: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (child, animation) {
                  return ScaleTransition(scale: animation, child: child);
                },
                child: Icon(
                  widget.isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                  key: ValueKey(widget.isBookmarked),
                  color: widget.isBookmarked
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildLabeledButton(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _isAnimating ? null : _toggleBookmark,
                onLongPress: _showBookmarkOptions,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: widget.isBookmarked
                        ? Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3)
                        : Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: widget.isBookmarked
                          ? Theme.of(context).colorScheme.primary.withOpacity(0.5)
                          : Theme.of(context).colorScheme.outline.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        transitionBuilder: (child, animation) {
                          return ScaleTransition(scale: animation, child: child);
                        },
                        child: Icon(
                          widget.isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                          key: ValueKey(widget.isBookmarked),
                          size: 18,
                          color: widget.isBookmarked
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        widget.isBookmarked ? 'Signet ajouté' : 'Ajouter signet',
                        style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          color: widget.isBookmarked
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                          fontWeight: widget.isBookmarked 
                              ? FontWeight.bold 
                              : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  void _toggleBookmark() async {
    if (_isAnimating) return;
    
    setState(() {
      _isAnimating = true;
    });
    
    _animationController.forward().then((_) {
      _animationController.reverse().then((_) {
        setState(() {
          _isAnimating = false;
        });
      });
    });
    
    if (widget.isBookmarked) {
      // Remove bookmark
      widget.onBookmarkRemoved?.call(widget.sectionId);
      _showFeedback('Signet retiré', Icons.bookmark_remove);
    } else {
      // Add bookmark
      final bookmark = BookmarkData(
        id: '${widget.guideId}_${widget.sectionId}_${DateTime.now().millisecondsSinceEpoch}',
        guideId: widget.guideId,
        sectionId: widget.sectionId,
        title: widget.sectionTitle,
        bookmarkType: widget.bookmarkType,
        color: Theme.of(context).colorScheme.primary,
      );
      
      widget.onBookmarkAdded?.call(bookmark);
      _showFeedback('Signet ajouté', Icons.bookmark_add);
    }
  }
  
  void _showBookmarkOptions() {
    if (widget.isBookmarked) {
      _showBookmarkManagementDialog();
    } else {
      _showBookmarkCreationDialog();
    }
  }
  
  void _showBookmarkCreationDialog() {
    showDialog(
      context: context,
      builder: (context) => BookmarkCreationDialog(
        guideId: widget.guideId,
        sectionId: widget.sectionId,
        sectionTitle: widget.sectionTitle,
        onBookmarkCreated: (bookmark) {
          widget.onBookmarkAdded?.call(bookmark);
          _showFeedback('Signet personnalisé créé', Icons.bookmark_add);
        },
      ),
    );
  }
  
  void _showBookmarkManagementDialog() {
    showDialog(
      context: context,
      builder: (context) => BookmarkManagementDialog(
        guideId: widget.guideId,
        sectionId: widget.sectionId,
        onBookmarkRemoved: () {
          widget.onBookmarkRemoved?.call(widget.sectionId);
          _showFeedback('Signet retiré', Icons.bookmark_remove);
        },
      ),
    );
  }
  
  void _showFeedback(String message, IconData icon) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 18),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
  
  /// Factory method for compact bookmark button
  static Widget compact({
    required String guideId,
    required String sectionId,
    required String sectionTitle,
    required bool isBookmarked,
    Function(BookmarkData)? onBookmarkAdded,
    Function(String)? onBookmarkRemoved,
  }) {
    return BookmarkButton(
      guideId: guideId,
      sectionId: sectionId,
      sectionTitle: sectionTitle,
      isBookmarked: isBookmarked,
      onBookmarkAdded: onBookmarkAdded,
      onBookmarkRemoved: onBookmarkRemoved,
      isCompact: true,
    );
  }
  
  /// Factory method for labeled bookmark button
  static Widget labeled({
    required String guideId,
    required String sectionId,
    required String sectionTitle,
    required bool isBookmarked,
    Function(BookmarkData)? onBookmarkAdded,
    Function(String)? onBookmarkRemoved,
  }) {
    return BookmarkButton(
      guideId: guideId,
      sectionId: sectionId,
      sectionTitle: sectionTitle,
      isBookmarked: isBookmarked,
      onBookmarkAdded: onBookmarkAdded,
      onBookmarkRemoved: onBookmarkRemoved,
      showLabel: true,
    );
  }
}

/// Dialog for creating custom bookmarks
class BookmarkCreationDialog extends StatefulWidget {
  final String guideId;
  final String sectionId;
  final String sectionTitle;
  final Function(BookmarkData) onBookmarkCreated;

  const BookmarkCreationDialog({
    super.key,
    required this.guideId,
    required this.sectionId,
    required this.sectionTitle,
    required this.onBookmarkCreated,
  });

  @override
  State<BookmarkCreationDialog> createState() => _BookmarkCreationDialogState();
}

class _BookmarkCreationDialogState extends State<BookmarkCreationDialog> {
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagController = TextEditingController();
  
  BookmarkType _selectedType = BookmarkType.section;
  Color _selectedColor = Colors.blue;
  List<String> _tags = [];
  
  final List<Color> _availableColors = [
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.red,
    Colors.teal,
    Colors.pink,
    Colors.indigo,
  ];
  
  @override
  void initState() {
    super.initState();
    _titleController.text = widget.sectionTitle;
  }
  
  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 20),
            _buildTitleField(),
            const SizedBox(height: 16),
            _buildDescriptionField(),
            const SizedBox(height: 16),
            _buildTypeSelector(),
            const SizedBox(height: 16),
            _buildColorSelector(),
            const SizedBox(height: 16),
            _buildTagsField(),
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.bookmark_add,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Créer un signet',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Personnalisez votre signet',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildTitleField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Titre du signet',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'Entrez le titre de votre signet',
          ),
          maxLength: 100,
        ),
      ],
    );
  }
  
  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description (optionnelle)',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'Ajoutez une description personnelle',
          ),
          maxLines: 3,
          maxLength: 200,
        ),
      ],
    );
  }
  
  Widget _buildTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Type de signet',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: BookmarkType.values.map((type) {
            final isSelected = type == _selectedType;
            return FilterChip(
              selected: isSelected,
              label: Text(_getTypeLabel(type)),
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedType = type;
                  });
                }
              },
            );
          }).toList(),
        ),
      ],
    );
  }
  
  Widget _buildColorSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Couleur du signet',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _availableColors.map((color) {
            final isSelected = color == _selectedColor;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedColor = color;
                });
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected 
                        ? Theme.of(context).colorScheme.outline
                        : Colors.transparent,
                    width: 3,
                  ),
                ),
                child: isSelected
                    ? const Icon(Icons.check, color: Colors.white)
                    : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
  
  Widget _buildTagsField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags (optionnel)',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _tagController,
          decoration: InputDecoration(
            border: const OutlineInputBorder(),
            hintText: 'Ajoutez un tag et appuyez sur Entrée',
            suffixIcon: IconButton(
              onPressed: _addTag,
              icon: const Icon(Icons.add),
            ),
          ),
          onFieldSubmitted: (value) => _addTag(),
        ),
        if (_tags.isNotEmpty) ...[
          const SizedBox(height: 8),
          Wrap(
            spacing: 4,
            runSpacing: 4,
            children: _tags.map((tag) {
              return Chip(
                label: Text(tag),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () {
                  setState(() {
                    _tags.remove(tag);
                  });
                },
              );
            }).toList(),
          ),
        ],
      ],
    );
  }
  
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        const SizedBox(width: 8),
        ElevatedButton.icon(
          onPressed: _createBookmark,
          icon: const Icon(Icons.bookmark_add),
          label: const Text('Créer le signet'),
          style: ElevatedButton.styleFrom(
            backgroundColor: _selectedColor,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }
  
  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() {
        _tags.add(tag);
        _tagController.clear();
      });
    }
  }
  
  void _createBookmark() {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Le titre est requis')),
      );
      return;
    }
    
    final bookmark = BookmarkData(
      id: '${widget.guideId}_${widget.sectionId}_${DateTime.now().millisecondsSinceEpoch}',
      guideId: widget.guideId,
      sectionId: widget.sectionId,
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      bookmarkType: _selectedType,
      color: _selectedColor,
      tags: _tags,
    );
    
    widget.onBookmarkCreated(bookmark);
    Navigator.of(context).pop();
  }
  
  String _getTypeLabel(BookmarkType type) {
    switch (type) {
      case BookmarkType.section:
        return 'Section';
      case BookmarkType.specificContent:
        return 'Contenu spécifique';
      case BookmarkType.personalNote:
        return 'Note personnelle';
    }
  }
}

/// Dialog for managing existing bookmarks
class BookmarkManagementDialog extends StatelessWidget {
  final String guideId;
  final String sectionId;
  final VoidCallback onBookmarkRemoved;

  const BookmarkManagementDialog({
    super.key,
    required this.guideId,
    required this.sectionId,
    required this.onBookmarkRemoved,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.bookmark),
          SizedBox(width: 8),
          Text('Gérer le signet'),
        ],
      ),
      content: const Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('Que souhaitez-vous faire avec ce signet ?'),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        TextButton.icon(
          onPressed: () {
            Navigator.of(context).pop();
            // TODO: Open bookmark editing dialog
          },
          icon: const Icon(Icons.edit),
          label: const Text('Modifier'),
        ),
        ElevatedButton.icon(
          onPressed: () {
            Navigator.of(context).pop();
            onBookmarkRemoved();
          },
          icon: const Icon(Icons.delete),
          label: const Text('Supprimer'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }
}