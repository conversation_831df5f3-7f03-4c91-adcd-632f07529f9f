import 'package:flutter/material.dart';
import '../../models/guide/guide_section_data.dart';
import '../../services/guide_content_service.dart';
import '../../services/guide_progress_service.dart';
import 'breadcrumb_navigation.dart';
import 'guide_progress_indicator.dart';
import 'guide_accordion_view.dart';
import 'guide_search_delegate.dart';

/// Reusable scaffold for all guide screens
class GuideScaffold extends StatefulWidget {
  final String guideId;
  final String title;
  final List<BreadcrumbItem> breadcrumbs;
  final Widget? floatingActionButton;
  final List<Widget>? actions;
  final bool showSearch;
  final bool showProgress;
  final bool allowMultipleExpanded;
  final String? initialExpandedSection;
  final Function(String sectionId)? onSectionCompleted;
  final VoidCallback? onBackPressed;

  const GuideScaffold({
    super.key,
    required this.guideId,
    required this.title,
    required this.breadcrumbs,
    this.floatingActionButton,
    this.actions,
    this.showSearch = true,
    this.showProgress = true,
    this.allowMultipleExpanded = false,
    this.initialExpandedSection,
    this.onSectionCompleted,
    this.onBackPressed,
  });

  @override
  State<GuideScaffold> createState() => _GuideScaffoldState();
}

class _GuideScaffoldState extends State<GuideScaffold> {
  final GuideContentService _contentService = GuideContentService();
  final GuideProgressService _progressService = GuideProgressService();
  final GlobalKey<GuideAccordionViewState> _accordionKey = GlobalKey();
  
  List<GuideSectionData> _sections = [];
  bool _isLoading = true;
  String? _error;
  double _overallProgress = 0.0;
  int _completedSections = 0;

  @override
  void initState() {
    super.initState();
    _loadContent();
  }

  Future<void> _loadContent() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Load guide content
      final sections = await _contentService.loadGuideContent(widget.guideId);
      
      // Load progress data
      final sectionIds = sections.map((s) => s.id).toList();
      final progress = await _progressService.getGuideCompletionPercentage(
        widget.guideId, 
        sectionIds,
      );
      final completedSections = await _progressService.getCompletedSections(widget.guideId);

      if (mounted) {
        setState(() {
          _sections = sections;
          _overallProgress = progress;
          _completedSections = completedSections.length;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Erreur lors du chargement du contenu: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: _buildBody(context),
      floatingActionButton: widget.floatingActionButton ?? _buildDefaultFAB(context),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);
    
    return AppBar(
      title: Text(widget.title),
      leading: widget.onBackPressed != null
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: widget.onBackPressed,
            )
          : null,
      actions: [
        if (widget.showSearch && _sections.isNotEmpty)
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _openSearch(context),
            tooltip: 'Rechercher',
          ),
        ...?widget.actions,
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Column(
          children: [
            // Breadcrumb navigation
            Container(
              color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
              child: BreadcrumbNavigation(
                items: widget.breadcrumbs,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
            // Progress indicator
            if (widget.showProgress && _sections.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                child: GuideProgressIndicator(
                  progress: _overallProgress,
                  completedSections: _completedSections,
                  totalSections: _sections.length,
                  showText: true,
                  showPercentage: true,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Chargement du contenu...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return _buildErrorState(context);
    }

    if (_sections.isEmpty) {
      return _buildEmptyState(context);
    }

    return RefreshIndicator(
      onRefresh: _loadContent,
      child: GuideAccordionView(
        key: _accordionKey,
        sections: _sections,
        guideId: widget.guideId,
        allowMultipleExpanded: widget.allowMultipleExpanded,
        initialExpandedSection: widget.initialExpandedSection,
        onSectionCompleted: (sectionId) {
          widget.onSectionCompleted?.call(sectionId);
          _refreshProgress();
        },
        onScrollProgress: (sectionId, progress) {
          // Handle scroll progress if needed
        },
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Erreur de chargement',
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadContent,
              icon: const Icon(Icons.refresh),
              label: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.menu_book_outlined,
            size: 64,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun contenu disponible',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Ce guide ne contient pas encore de sections.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget? _buildDefaultFAB(BuildContext context) {
    if (_sections.isEmpty) return null;
    
    return FloatingActionButton.extended(
      onPressed: _showQuickNavigation,
      icon: const Icon(Icons.list),
      label: const Text('Navigation'),
    );
  }

  void _openSearch(BuildContext context) {
    showSearch(
      context: context,
      delegate: GuideSearchDelegate(
        guideId: widget.guideId,
        sections: _sections,
        onSectionSelected: (sectionId) {
          // Expand the selected section
          _accordionKey.currentState?.expandSection(sectionId);
        },
      ),
    );
  }

  void _showQuickNavigation() {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildQuickNavigationSheet(context),
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
    );
  }

  Widget _buildQuickNavigationSheet(BuildContext context) {
    final theme = Theme.of(context);
    
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      maxChildSize: 0.9,
      minChildSize: 0.3,
      builder: (context, scrollController) {
        return Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Row(
                children: [
                  Text(
                    'Navigation rapide',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                controller: scrollController,
                itemCount: _sections.length,
                itemBuilder: (context, index) {
                  final section = _sections[index];
                  return ListTile(
                    leading: Icon(_getTypeIcon(section.type)),
                    title: Text(section.title),
                    subtitle: Text('${section.estimatedReadTime} min'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      Navigator.pop(context);
                      _accordionKey.currentState?.expandSection(section.id);
                    },
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _refreshProgress() async {
    final sectionIds = _sections.map((s) => s.id).toList();
    final progress = await _progressService.getGuideCompletionPercentage(
      widget.guideId, 
      sectionIds,
    );
    final completedSections = await _progressService.getCompletedSections(widget.guideId);

    if (mounted) {
      setState(() {
        _overallProgress = progress;
        _completedSections = completedSections.length;
      });
    }
  }

  IconData _getTypeIcon(GuideSectionType type) {
    switch (type) {
      case GuideSectionType.list:
        return Icons.list;
      case GuideSectionType.calculator:
        return Icons.calculate;
      case GuideSectionType.example:
        return Icons.lightbulb_outline;
      case GuideSectionType.definition:
        return Icons.book;
      case GuideSectionType.formula:
        return Icons.functions;
      case GuideSectionType.table:
        return Icons.table_chart;
      case GuideSectionType.widget:
        return Icons.widgets;
      default:
        return Icons.article;
    }
  }
}
