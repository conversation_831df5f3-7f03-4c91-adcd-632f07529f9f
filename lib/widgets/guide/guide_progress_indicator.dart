import 'package:flutter/material.dart';

/// Progress indicator widget for guide sections
class GuideProgressIndicator extends StatelessWidget {
  final double progress; // 0.0 to 1.0
  final int completedSections;
  final int totalSections;
  final bool showText;
  final bool showPercentage;
  final double height;
  final Color? backgroundColor;
  final Color? progressColor;
  final BorderRadius? borderRadius;
  final String? customLabel;

  const GuideProgressIndicator({
    super.key,
    required this.progress,
    required this.completedSections,
    required this.totalSections,
    this.showText = true,
    this.showPercentage = false,
    this.height = 8,
    this.backgroundColor,
    this.progressColor,
    this.borderRadius,
    this.customLabel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final clampedProgress = progress.clamp(0.0, 1.0);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showText || showPercentage || customLabel != null) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (customLabel != null)
                Text(
                  customLabel!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                )
              else if (showText)
                Text(
                  '$completedSections sur $totalSections sections terminées',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              if (showPercentage)
                Text(
                  '${(clampedProgress * 100).round()}%',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: _getProgressColor(context, clampedProgress),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
        ],
        _buildProgressBar(context, clampedProgress),
      ],
    );
  }

  Widget _buildProgressBar(BuildContext context, double progress) {
    final theme = Theme.of(context);
    final effectiveBackgroundColor = backgroundColor ?? 
        theme.colorScheme.surfaceVariant.withOpacity(0.3);
    final effectiveProgressColor = progressColor ?? 
        _getProgressColor(context, progress);
    final effectiveBorderRadius = borderRadius ?? 
        BorderRadius.circular(height / 2);

    return Container(
      height: height,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: effectiveBorderRadius,
      ),
      child: ClipRRect(
        borderRadius: effectiveBorderRadius,
        child: LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.transparent,
          valueColor: AlwaysStoppedAnimation<Color>(effectiveProgressColor),
          minHeight: height,
        ),
      ),
    );
  }

  Color _getProgressColor(BuildContext context, double progress) {
    final theme = Theme.of(context);
    
    if (progress >= 1.0) {
      return theme.colorScheme.primary; // Completed
    } else if (progress >= 0.75) {
      return Colors.green; // Almost complete
    } else if (progress >= 0.5) {
      return Colors.orange; // Half way
    } else if (progress >= 0.25) {
      return Colors.amber; // Getting started
    } else {
      return theme.colorScheme.outline; // Just started
    }
  }
}

/// Circular progress indicator for guide sections
class CircularGuideProgressIndicator extends StatelessWidget {
  final double progress; // 0.0 to 1.0
  final double size;
  final double strokeWidth;
  final bool showPercentage;
  final Color? backgroundColor;
  final Color? progressColor;

  const CircularGuideProgressIndicator({
    super.key,
    required this.progress,
    this.size = 60,
    this.strokeWidth = 6,
    this.showPercentage = true,
    this.backgroundColor,
    this.progressColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final clampedProgress = progress.clamp(0.0, 1.0);
    final effectiveProgressColor = progressColor ?? 
        _getProgressColor(context, clampedProgress);

    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          CircularProgressIndicator(
            value: clampedProgress,
            strokeWidth: strokeWidth,
            backgroundColor: backgroundColor ?? 
                theme.colorScheme.surfaceVariant.withOpacity(0.3),
            valueColor: AlwaysStoppedAnimation<Color>(effectiveProgressColor),
          ),
          if (showPercentage)
            Text(
              '${(clampedProgress * 100).round()}%',
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: effectiveProgressColor,
              ),
            ),
        ],
      ),
    );
  }

  Color _getProgressColor(BuildContext context, double progress) {
    final theme = Theme.of(context);
    
    if (progress >= 1.0) {
      return theme.colorScheme.primary;
    } else if (progress >= 0.75) {
      return Colors.green;
    } else if (progress >= 0.5) {
      return Colors.orange;
    } else if (progress >= 0.25) {
      return Colors.amber;
    } else {
      return theme.colorScheme.outline;
    }
  }
}

/// Compact progress indicator for list items
class CompactProgressIndicator extends StatelessWidget {
  final double progress; // 0.0 to 1.0
  final bool isCompleted;
  final double size;

  const CompactProgressIndicator({
    super.key,
    required this.progress,
    required this.isCompleted,
    this.size = 24,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    if (isCompleted) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: theme.colorScheme.primary,
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.check,
          color: theme.colorScheme.onPrimary,
          size: size * 0.6,
        ),
      );
    }

    final clampedProgress = progress.clamp(0.0, 1.0);
    
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        value: clampedProgress,
        strokeWidth: 3,
        backgroundColor: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        valueColor: AlwaysStoppedAnimation<Color>(
          _getProgressColor(context, clampedProgress),
        ),
      ),
    );
  }

  Color _getProgressColor(BuildContext context, double progress) {
    final theme = Theme.of(context);
    
    if (progress >= 0.75) {
      return Colors.green;
    } else if (progress >= 0.5) {
      return Colors.orange;
    } else if (progress >= 0.25) {
      return Colors.amber;
    } else {
      return theme.colorScheme.outline;
    }
  }
}

/// Progress indicator with milestone markers
class MilestoneProgressIndicator extends StatelessWidget {
  final double progress; // 0.0 to 1.0
  final List<String> milestones;
  final double height;

  const MilestoneProgressIndicator({
    super.key,
    required this.progress,
    required this.milestones,
    this.height = 12,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final clampedProgress = progress.clamp(0.0, 1.0);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: height,
          child: Stack(
            children: [
              // Background bar
              Container(
                height: height,
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(height / 2),
                ),
              ),
              // Progress bar
              FractionallySizedBox(
                widthFactor: clampedProgress,
                child: Container(
                  height: height,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: BorderRadius.circular(height / 2),
                  ),
                ),
              ),
              // Milestone markers
              ...milestones.asMap().entries.map((entry) {
                final index = entry.key;
                final milestone = entry.value;
                final position = (index + 1) / milestones.length;
                final isReached = clampedProgress >= position;
                
                return Positioned(
                  left: position * MediaQuery.of(context).size.width * 0.8 - 6,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: isReached 
                          ? theme.colorScheme.primary 
                          : theme.colorScheme.outline,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: theme.colorScheme.surface,
                        width: 2,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // Milestone labels
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: milestones.map((milestone) {
            return Expanded(
              child: Text(
                milestone,
                style: theme.textTheme.bodySmall,
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
