import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// @deprecated This widget will be replaced by GuideAccordionView.
/// Use GuideScaffold with GuideAccordionView for new implementations.
///
/// Migration guide:
/// 1. Replace CustomTabView with GuideScaffold
/// 2. Convert tab content to GuideSectionData objects
/// 3. Use GuideContentService to load content
/// 4. Implement progress tracking with GuideProgressService
class CustomTabView extends StatefulWidget {
  final String jsonPath;
  final List<Widget> sections;
  final bool useAccordionMode; // Flag for gradual migration

  const CustomTabView({
    super.key,
    required this.jsonPath,
    required this.sections,
    this.useAccordionMode = false, // Default to legacy mode
  });

  @override
  State<CustomTabView> createState() => _CustomTabViewState();
}

class _CustomTabViewState extends State<CustomTabView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Map<String, dynamic>> _tabs = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: widget.sections.length, vsync: this);
    _loadTabs();
  }

  Future<void> _loadTabs() async {
    try {
      final String jsonString = await rootBundle.loadString(widget.jsonPath);
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      if (mounted) {
        setState(() {
          _tabs = List<Map<String, dynamic>>.from(jsonData['tabs']);
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading tabs: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isWideScreen = MediaQuery.of(context).size.width > 900;

    if (_isLoading) {
      return Scaffold(
        body: Center(
          child: CircularProgressIndicator(
            color: colorScheme.primary,
          ),
        ),
      );
    }

    if (_tabs.isEmpty) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Erreur de chargement des onglets',
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.error,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: colorScheme.surface,
        title: Text(
          'Droits d\'Enregistrement',
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        bottom: !isWideScreen
            ? PreferredSize(
                preferredSize: const Size.fromHeight(kToolbarHeight),
                child: Container(
                  decoration: BoxDecoration(
                    color: colorScheme.surface,
                    border: Border(
                      bottom: BorderSide(
                        color: colorScheme.outlineVariant,
                        width: 1,
                      ),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: colorScheme.shadow.withOpacity(0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TabBar(
                    controller: _tabController,
                    isScrollable: true,
                    indicatorColor: colorScheme.primary,
                    indicatorWeight: 3,
                    labelColor: colorScheme.primary,
                    unselectedLabelColor: colorScheme.onSurfaceVariant,
                    labelStyle: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    unselectedLabelStyle: textTheme.titleSmall,
                    indicatorSize: TabBarIndicatorSize.label,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    tabs: _buildTabs(),
                  ),
                ),
              )
            : null,
      ),
      body: Row(
        children: [
          if (isWideScreen && _tabs.length >= 2)
            Container(
              width: 200,
              decoration: BoxDecoration(
                color: colorScheme.surface,
                border: Border(
                  right: BorderSide(
                    color: colorScheme.outlineVariant,
                    width: 1,
                  ),
                ),
              ),
              child: NavigationRail(
                selectedIndex: _tabController.index,
                onDestinationSelected: (index) {
                  setState(() {
                    _tabController.animateTo(index);
                  });
                },
                labelType: NavigationRailLabelType.all,
                backgroundColor: colorScheme.surface,
                selectedIconTheme: IconThemeData(
                  color: colorScheme.primary,
                  size: 28,
                ),
                unselectedIconTheme: IconThemeData(
                  color: colorScheme.onSurfaceVariant,
                  size: 24,
                ),
                selectedLabelTextStyle: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
                unselectedLabelTextStyle: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                useIndicator: true,
                indicatorColor: colorScheme.primaryContainer,
                destinations: _buildRailDestinations(),
              ),
            ),
          Expanded(
            child: Container(
              color: colorScheme.surface,
              child: TabBarView(
                controller: _tabController,
                children: widget.sections
                    .map(
                      (section) => Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: section,
                      ),
                    )
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Tab> _buildTabs() {
    return _tabs.map((tab) {
      return Tab(
        icon: Icon(
          _getIconData(tab['icon']),
          size: 24,
        ),
        text: tab['text'],
      );
    }).toList();
  }

  List<NavigationRailDestination> _buildRailDestinations() {
    return _tabs.map((tab) {
      return NavigationRailDestination(
        icon: Icon(_getIconData(tab['icon'])),
        selectedIcon: Icon(
          _getIconData(tab['icon']),
          size: 28,
        ),
        label: Text(tab['text']),
        padding: const EdgeInsets.symmetric(vertical: 8),
      );
    }).toList();
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'new_releases':
        return Icons.new_releases;
      case 'percent':
        return Icons.percent;
      case 'gavel':
        return Icons.gavel;
      case 'assignment':
        return Icons.assignment;
      case 'school':
        return Icons.school;
      default:
        return Icons.circle;
    }
  }
}
