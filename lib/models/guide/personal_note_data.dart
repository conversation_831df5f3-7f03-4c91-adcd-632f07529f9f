import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

part 'personal_note_data.g.dart';

/// Enumeration for different types of personal notes
enum NoteType {
  text,
  checklist,
  reminder,
}

/// Data model for personal notes with Hive persistence
@HiveType(typeId: 4)
class PersonalNoteData extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String guideId;

  @HiveField(2)
  final String sectionId;

  @HiveField(3)
  String title;

  @HiveField(4)
  String content;

  @HiveField(5)
  final NoteType noteType;

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  DateTime lastModified;

  @HiveField(8)
  List<String> tags;

  @HiveField(9)
  final bool isPrivate;

  @HiveField(10)
  final List<String> attachments; // For future file attachments

  @HiveField(11)
  final DateTime? reminderDate;

  @HiveField(12)
  String formattedContent; // For basic markdown support

  PersonalNoteData({
    required this.id,
    required this.guideId,
    required this.sectionId,
    required this.title,
    required this.content,
    this.noteType = NoteType.text,
    DateTime? createdAt,
    DateTime? lastModified,
    this.tags = const [],
    this.isPrivate = false,
    this.attachments = const [],
    this.reminderDate,
    String? formattedContent,
  }) : createdAt = createdAt ?? DateTime.now(),
       lastModified = lastModified ?? DateTime.now(),
       formattedContent = formattedContent ?? content;

  /// Creates a PersonalNoteData from JSON
  factory PersonalNoteData.fromJson(Map<String, dynamic> json) {
    return PersonalNoteData(
      id: json['id'] ?? '',
      guideId: json['guideId'] ?? '',
      sectionId: json['sectionId'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      noteType: _parseNoteType(json['noteType']),
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      lastModified: DateTime.tryParse(json['lastModified'] ?? '') ?? DateTime.now(),
      tags: List<String>.from(json['tags'] ?? []),
      isPrivate: json['isPrivate'] ?? false,
      attachments: List<String>.from(json['attachments'] ?? []),
      reminderDate: json['reminderDate'] != null 
          ? DateTime.tryParse(json['reminderDate']) 
          : null,
      formattedContent: json['formattedContent'],
    );
  }

  /// Converts PersonalNoteData to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'guideId': guideId,
      'sectionId': sectionId,
      'title': title,
      'content': content,
      'noteType': noteType.name,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
      'tags': tags,
      'isPrivate': isPrivate,
      'attachments': attachments,
      'reminderDate': reminderDate?.toIso8601String(),
      'formattedContent': formattedContent,
    };
  }

  /// Creates a copy with updated fields
  PersonalNoteData copyWith({
    String? id,
    String? guideId,
    String? sectionId,
    String? title,
    String? content,
    NoteType? noteType,
    DateTime? createdAt,
    DateTime? lastModified,
    List<String>? tags,
    bool? isPrivate,
    List<String>? attachments,
    DateTime? reminderDate,
    String? formattedContent,
  }) {
    return PersonalNoteData(
      id: id ?? this.id,
      guideId: guideId ?? this.guideId,
      sectionId: sectionId ?? this.sectionId,
      title: title ?? this.title,
      content: content ?? this.content,
      noteType: noteType ?? this.noteType,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
      tags: tags ?? this.tags,
      isPrivate: isPrivate ?? this.isPrivate,
      attachments: attachments ?? this.attachments,
      reminderDate: reminderDate ?? this.reminderDate,
      formattedContent: formattedContent ?? this.formattedContent,
    );
  }

  /// Updates the note content and last modified time
  void updateContent(String newContent, {String? newTitle}) {
    content = newContent;
    formattedContent = newContent;
    if (newTitle != null) {
      title = newTitle;
    }
    lastModified = DateTime.now();
    save(); // Save to Hive
  }

  /// Adds a tag to the note
  void addTag(String tag) {
    if (!tags.contains(tag)) {
      tags = List<String>.from(tags)..add(tag);
      lastModified = DateTime.now();
      save();
    }
  }

  /// Removes a tag from the note
  void removeTag(String tag) {
    tags = List<String>.from(tags)..remove(tag);
    lastModified = DateTime.now();
    save();
  }

  /// Gets searchable text from the note
  String getSearchableText() {
    final buffer = StringBuffer();
    buffer.write(title.toLowerCase());
    buffer.write(' ');
    buffer.write(content.toLowerCase());
    buffer.write(' ');
    buffer.write(tags.join(' ').toLowerCase());
    return buffer.toString();
  }

  /// Validates note data integrity
  bool isValid() {
    return id.isNotEmpty &&
           guideId.isNotEmpty &&
           sectionId.isNotEmpty &&
           title.isNotEmpty;
  }

  /// Gets display text for note type
  String getNoteTypeLabel() {
    switch (noteType) {
      case NoteType.text:
        return 'Texte';
      case NoteType.checklist:
        return 'Liste de contrôle';
      case NoteType.reminder:
        return 'Rappel';
    }
  }

  /// Gets notes by tag
  static List<PersonalNoteData> getTaggedNotes(
    List<PersonalNoteData> notes, 
    String tag
  ) {
    return notes.where((note) => note.tags.contains(tag)).toList();
  }

  /// Checks if the note has a reminder that's due
  bool hasOverdueReminder() {
    if (reminderDate == null) return false;
    return DateTime.now().isAfter(reminderDate!);
  }

  /// Gets formatted content preview (first 100 characters)
  String getContentPreview() {
    if (content.length <= 100) return content;
    return '${content.substring(0, 97)}...';
  }

  /// Parses note type from string
  static NoteType _parseNoteType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'checklist':
        return NoteType.checklist;
      case 'reminder':
        return NoteType.reminder;
      default:
        return NoteType.text;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PersonalNoteData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PersonalNoteData(id: $id, title: $title, guideId: $guideId, sectionId: $sectionId)';
  }
}